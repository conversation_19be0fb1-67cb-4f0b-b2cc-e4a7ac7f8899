/**
 * English Proficiency Assessment Module
 * Handles the English assessment flow for student users
 */

class EnglishAssessment {
  constructor() {
    this.timeLimit = 30 * 60; // 30 minutes in seconds
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;
    this.isSubmitted = false;
    // Removed minimum response length requirement to allow natural assessment

    // New properties for preliminary questions
    this.currentStage = 'preliminary'; // 'preliminary' or 'essay'
    this.preliminaryQuestions = [];
    this.preliminaryResponses = [];
    this.currentQuestionIndex = 0;

    // Enhanced response tracking for transparency
    this.assessmentStartTime = null;
    this.questionStartTime = null;
    this.essayStartTime = null;
    this.detailedResponses = {
      preliminaryQuestions: [],
      essayResponse: null,
      assessmentMetadata: {}
    };
  }

  /**
   * Initialize the English assessment
   */
  async init() {
    this.resetAssessment();
    await this.checkIfRetake();

    // Initialize assessment metadata
    this.initializeAssessmentMetadata();

    // Hide essay container immediately and show loading
    this.hideEssayContainer();
    this.showQuestionGenerationLoading();

    try {
      // Generate preliminary questions first
      await this.generatePreliminaryQuestions();

      // Hide loading and start with preliminary questions
      this.hideQuestionGenerationLoading();
      this.showPreliminaryQuestions();

    } catch (error) {
      console.error('Error during question generation:', error);
      this.hideQuestionGenerationLoading();
      // Fallback to essay if question generation fails
      this.proceedToEssay();
    }

    this.setupEventListeners();
    this.startTimer();
    console.log('English assessment initialized with preliminary questions');
  }

  /**
   * Initialize assessment metadata for comprehensive tracking
   */
  initializeAssessmentMetadata() {
    this.assessmentStartTime = new Date();
    this.detailedResponses.assessmentMetadata = {
      startTime: this.assessmentStartTime,
      browserInfo: this.getBrowserInfo(),
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language
    };

    console.log('Assessment metadata initialized:', this.detailedResponses.assessmentMetadata);
  }

  /**
   * Get browser information for metadata
   */
  getBrowserInfo() {
    const ua = navigator.userAgent;
    let browser = 'Unknown';

    if (ua.includes('Chrome')) browser = 'Chrome';
    else if (ua.includes('Firefox')) browser = 'Firefox';
    else if (ua.includes('Safari')) browser = 'Safari';
    else if (ua.includes('Edge')) browser = 'Edge';

    return browser;
  }

  /**
   * Check if this is a retake and show appropriate message
   */
  async checkIfRetake() {
    try {
      const email = document.getElementById('email').value.trim();

      if (!email || typeof db === 'undefined' || typeof userCompany === 'undefined') {
        return;
      }

      const companyRef = db.collection('companies').doc(userCompany);
      const userRef = companyRef.collection('users').doc(email);
      const userDoc = await userRef.get();
      const userData = userDoc.data();

      if (userData && userData.englishAssessmentCompleted && userData.englishProficiencyScore < 16) {
        // This is a retake - show retake message
        this.showRetakeMessage(userData.englishProficiencyScore, userData.englishProficiencyLevel);
      }
    } catch (error) {
      console.error('Error checking retake status:', error);
      // Continue with normal assessment if check fails
    }
  }

  /**
   * Show retake message to encourage the student
   */
  showRetakeMessage(previousScore, previousLevel) {
    const headerElement = document.querySelector('.english-header p');
    if (headerElement) {
      headerElement.innerHTML = `
        <strong>Retake Opportunity:</strong> Your previous score was ${previousScore}/21 (${previousLevel} level).
        You need 16+ points to proceed to digital skills assessment. Take your time and show your best English skills!
      `;
      headerElement.style.background = 'rgba(255, 255, 255, 0.1)';
      headerElement.style.padding = '0.5rem';
      headerElement.style.borderRadius = '4px';
      headerElement.style.marginTop = '0.5rem';
    }
  }

  /**
   * Generate preliminary questions using AI
   */
  async generatePreliminaryQuestions() {
    try {
      console.log('Generating preliminary questions...');

      const response = await fetch('/api/generate-preliminary-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studentLevel: document.getElementById('student-level')?.value || 'beginner',
          questionCount: 8
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate questions: ${response.statusText}`);
      }

      const data = await response.json();
      this.preliminaryQuestions = data.questions || this.getFallbackQuestions();
      console.log('Preliminary questions generated:', this.preliminaryQuestions);

    } catch (error) {
      console.error('Error generating preliminary questions:', error);
      this.preliminaryQuestions = this.getFallbackQuestions();
    }
  }

  /**
   * Fallback questions if AI generation fails
   */
  getFallbackQuestions() {
    return [
      {
        type: 'grammar',
        question: 'Rewrite this sentence with correct grammar:',
        content: 'She don\'t like to goes to the store yesterday.',
        correctAnswer: 'She didn\'t like to go to the store yesterday.'
      },
      {
        type: 'vocabulary',
        question: 'Choose the best word to complete the sentence:',
        content: 'The weather today is very _____ for a picnic.',
        options: ['terrible', 'perfect', 'impossible', 'dangerous'],
        correctAnswer: 'perfect'
      },
      {
        type: 'sentence',
        question: 'Put these words in the correct order to make a sentence:',
        content: ['school', 'to', 'walks', 'every', 'day', 'she'],
        correctAnswer: 'She walks to school every day.'
      },
      {
        type: 'drag-drop',
        question: 'Drag the words to form a correct sentence:',
        content: ['The', 'dog', 'is', 'running', 'in', 'the', 'park'],
        correctAnswer: 'The dog is running in the park.',
        shuffledWords: ['running', 'The', 'park', 'dog', 'in', 'is', 'the']
      },
      {
        type: 'interactive-grammar',
        question: 'Click on the words that need to be corrected:',
        content: 'He don\'t knows how to swim very good.',
        correctAnswer: 'He doesn\'t know how to swim very well.',
        mistakes: [
          {word: 'don\'t', correction: 'doesn\'t', position: 1},
          {word: 'knows', correction: 'know', position: 2},
          {word: 'good', correction: 'well', position: 7}
        ]
      },
      {
        type: 'vocab-matching',
        question: 'Match each word with its correct definition:',
        content: {
          words: ['happy', 'sad', 'angry', 'excited'],
          definitions: ['feeling joy', 'feeling upset', 'feeling mad', 'feeling enthusiastic']
        },
        correctAnswer: 'happy→feeling joy; sad→feeling upset; angry→feeling mad; excited→feeling enthusiastic'
      },
      {
        type: 'reading-comprehension',
        question: 'Read the passage and answer the question:',
        content: {
          passage: 'Tom goes to work by bus every morning. The bus arrives at 8:00 AM and takes 30 minutes to reach his office. Tom likes to read the newspaper during the journey.',
          question: 'How does Tom go to work?',
          options: ['By car', 'By bus', 'By train', 'On foot']
        },
        correctAnswer: 'By bus'
      },
      {
        type: 'grammar',
        question: 'Rewrite this sentence with correct grammar:',
        content: 'The children was playing in the park when it start to rain.',
        correctAnswer: 'The children were playing in the park when it started to rain.',
        mistakes: ['was', 'start']
      }
    ];
  }

  /**
   * Reset assessment state for retakes
   */
  resetAssessment() {
    // Reset timer
    this.timeRemaining = this.timeLimit;
    this.isSubmitted = false;

    // Clear any existing timer
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }

    // Reset preliminary questions state
    this.currentStage = 'preliminary';
    this.preliminaryResponses = [];
    this.currentQuestionIndex = 0;

    // Clear textarea
    const textarea = document.getElementById('english-response');
    if (textarea) {
      textarea.value = '';
    }

    // Reset submit button
    const submitBtn = document.getElementById('submit-english-assessment');
    if (submitBtn) {
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<span class="btn-text">Submit Assessment</span><span class="btn-icon">→</span>';
    }

    console.log('English assessment reset for retake');
  }

  /**
   * Hide essay container during preliminary questions
   */
  hideEssayContainer() {
    const essayContainer = document.querySelector('.english-question-container');
    if (essayContainer) {
      essayContainer.style.display = 'none';
      console.log('Essay container hidden for preliminary questions');
    }
  }

  /**
   * Show loading state during question generation
   */
  showQuestionGenerationLoading() {
    const container = document.getElementById('english-assessment-container');
    if (!container) return;

    // Create loading overlay specifically for question generation
    let loadingOverlay = document.getElementById('question-generation-loading');
    if (!loadingOverlay) {
      loadingOverlay = document.createElement('div');
      loadingOverlay.id = 'question-generation-loading';
      loadingOverlay.className = 'question-generation-loading';

      loadingOverlay.innerHTML = `
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">Generating your assessment questions...</div>
          <div class="loading-subtext">This will only take a moment</div>
        </div>
      `;

      // Insert after the timer container
      const timerContainer = document.querySelector('.timer-container');
      if (timerContainer) {
        timerContainer.insertAdjacentElement('afterend', loadingOverlay);
      }
    }

    // Show with smooth animation
    loadingOverlay.style.display = 'flex';
    setTimeout(() => {
      loadingOverlay.style.opacity = '1';
    }, 10);
  }

  /**
   * Hide question generation loading
   */
  hideQuestionGenerationLoading() {
    const loadingOverlay = document.getElementById('question-generation-loading');
    if (loadingOverlay) {
      loadingOverlay.style.opacity = '0';
      setTimeout(() => {
        loadingOverlay.style.display = 'none';
      }, 300);
    }
  }

  /**
   * Show preliminary questions interface
   */
  showPreliminaryQuestions() {
    const container = document.getElementById('english-assessment-container');
    if (!container) return;

    // Ensure essay container is hidden
    this.hideEssayContainer();

    // Create preliminary questions container
    let prelimContainer = document.getElementById('preliminary-questions-container');
    if (!prelimContainer) {
      prelimContainer = document.createElement('div');
      prelimContainer.id = 'preliminary-questions-container';
      prelimContainer.className = 'preliminary-questions-container';

      // Insert after the timer container
      const timerContainer = document.querySelector('.timer-container');
      if (timerContainer) {
        timerContainer.insertAdjacentElement('afterend', prelimContainer);
      }
    }

    // Show container with smooth transition
    prelimContainer.style.display = 'block';
    prelimContainer.style.opacity = '0';

    setTimeout(() => {
      prelimContainer.style.opacity = '1';
      this.renderCurrentQuestion();
    }, 100);
  }

  /**
   * Render the current preliminary question
   */
  renderCurrentQuestion() {
    const container = document.getElementById('preliminary-questions-container');
    if (!container || this.currentQuestionIndex >= this.preliminaryQuestions.length) {
      this.proceedToEssay();
      return;
    }

    const question = this.preliminaryQuestions[this.currentQuestionIndex];
    const questionNumber = this.currentQuestionIndex + 1;
    const totalQuestions = this.preliminaryQuestions.length;

    // Start timing for this question
    this.questionStartTime = new Date();

    container.innerHTML = `
      <div class="preliminary-question-wrapper">
        <div class="question-progress">
          <span class="progress-text">Question ${questionNumber} of ${totalQuestions}</span>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${(questionNumber / totalQuestions) * 100}%"></div>
          </div>
        </div>

        <div class="question-content">
          <h3>${question.question}</h3>
          <div class="question-body" id="question-body-${questionNumber}">
            ${this.renderQuestionByType(question, questionNumber)}
          </div>
        </div>

        <div class="question-actions">
          <button id="next-question-btn" class="question-btn primary" disabled>
            ${questionNumber === totalQuestions ? 'Continue to Writing Task' : 'Next Question'}
            <span class="btn-icon">→</span>
          </button>
        </div>
      </div>
    `;

    this.setupQuestionEventListeners(question, questionNumber);
  }

  /**
   * Render question content based on type
   */
  renderQuestionByType(question, questionNumber) {
    switch (question.type) {
      case 'grammar':
        return this.renderGrammarQuestion(question, questionNumber);
      case 'vocabulary':
        return this.renderVocabularyQuestion(question, questionNumber);
      case 'sentence':
        return this.renderSentenceQuestion(question, questionNumber);
      case 'drag-drop':
        return this.renderDragDropQuestion(question, questionNumber);
      case 'interactive-grammar':
        return this.renderInteractiveGrammarQuestion(question, questionNumber);
      case 'vocab-matching':
        return this.renderVocabMatchingQuestion(question, questionNumber);
      case 'reading-comprehension':
        return this.renderReadingComprehensionQuestion(question, questionNumber);
      default:
        return this.renderGrammarQuestion(question, questionNumber);
    }
  }



  /**
   * Render grammar correction question
   */
  renderGrammarQuestion(question, questionNumber) {
    return `
      <div class="grammar-question">
        <p class="instruction">Rewrite this sentence correctly:</p>
        <div class="original-sentence">
          "${question.content}"
        </div>
        <textarea
          id="grammar-response-${questionNumber}"
          class="grammar-textarea"
          placeholder="Type your version of the sentence here..."
          rows="3"
        ></textarea>
        <p class="natural-response-note">Write the sentence as you think it should be. There are no wrong answers - this helps us understand your English level.</p>
      </div>
    `;
  }

  /**
   * Render vocabulary multiple choice question
   */
  renderVocabularyQuestion(question, questionNumber) {
    const options = question.options.map((option, index) => `
      <label class="vocab-option">
        <input type="radio" name="vocab-${questionNumber}" value="${option}" />
        <span class="option-text">${option}</span>
      </label>
    `).join('');

    return `
      <div class="vocabulary-question">
        <p class="instruction">Choose the best word to complete the sentence:</p>
        <div class="sentence-with-blank">
          "${question.content}"
        </div>
        <div class="vocab-options">
          ${options}
        </div>
      </div>
    `;
  }

  /**
   * Render sentence construction question
   */
  renderSentenceQuestion(question, questionNumber) {
    const wordsDisplay = question.content.join(', ');

    return `
      <div class="sentence-question">
        <p class="instruction">Use these words to write a complete sentence:</p>
        <div class="word-bank-display">
          <strong>Words to use:</strong> ${wordsDisplay}
        </div>
        <textarea
          id="sentence-response-${questionNumber}"
          class="sentence-textarea"
          placeholder="Write a sentence using the words above..."
          rows="3"
        ></textarea>
        <p class="natural-response-note">Use all or some of the words to create a sentence. Write it as you think it should be.</p>
      </div>
    `;
  }

  /**
   * Render drag-and-drop sentence construction question
   */
  renderDragDropQuestion(question, questionNumber) {
    const shuffledWords = question.shuffledWords || question.content;
    const wordsHtml = shuffledWords.map((word, index) => `
      <div class="drag-word" draggable="true" data-word="${word}" data-index="${index}">
        ${word}
      </div>
    `).join('');

    return `
      <div class="drag-drop-question">
        <p class="instruction">Drag the words below to form a correct sentence:</p>
        <div class="drop-zone" id="drop-zone-${questionNumber}">
          <p class="drop-placeholder">Drag words here to build your sentence</p>
        </div>
        <div class="word-bank" id="word-bank-${questionNumber}">
          ${wordsHtml}
        </div>
        <p class="natural-response-note">Arrange the words in any order you think makes sense.</p>
      </div>
    `;
  }

  /**
   * Render interactive grammar correction question
   */
  renderInteractiveGrammarQuestion(question, questionNumber) {
    const words = question.content.split(' ');
    const wordsHtml = words.map((word, index) => `
      <span class="interactive-word" data-word="${word}" data-position="${index}" id="word-${questionNumber}-${index}">
        ${word}
      </span>
    `).join(' ');

    return `
      <div class="interactive-grammar-question">
        <p class="instruction">Click on words that need to be corrected:</p>
        <div class="sentence-interactive" id="sentence-interactive-${questionNumber}">
          ${wordsHtml}
        </div>
        <div class="corrections-display" id="corrections-${questionNumber}"></div>
        <p class="natural-response-note">Click on any words you think are incorrect. You can change your mind by clicking again.</p>
      </div>
    `;
  }

  /**
   * Render vocabulary matching question
   */
  renderVocabMatchingQuestion(question, questionNumber) {
    const words = question.content.words;
    const definitions = question.content.definitions;

    const wordsHtml = words.map((word, index) => `
      <div class="match-word" data-word="${word}" data-index="${index}">
        ${word}
      </div>
    `).join('');

    const definitionsHtml = definitions.map((definition, index) => `
      <div class="match-definition" data-definition="${definition}" data-index="${index}">
        ${definition}
      </div>
    `).join('');

    return `
      <div class="vocab-matching-question">
        <p class="instruction">Match each word with its correct definition by clicking:</p>
        <div class="matching-container">
          <div class="words-column">
            <h4>Words</h4>
            ${wordsHtml}
          </div>
          <div class="definitions-column">
            <h4>Definitions</h4>
            ${definitionsHtml}
          </div>
        </div>
        <div class="matches-display" id="matches-${questionNumber}"></div>
        <p class="natural-response-note">Click on a word, then click on its definition. Make your best guess.</p>
      </div>
    `;
  }

  /**
   * Render reading comprehension question
   */
  renderReadingComprehensionQuestion(question, questionNumber) {
    const options = question.content.options.map((option, index) => `
      <label class="reading-option">
        <input type="radio" name="reading-${questionNumber}" value="${option}" />
        <span class="option-text">${option}</span>
      </label>
    `).join('');

    return `
      <div class="reading-comprehension-question">
        <div class="reading-passage">
          <h4>Read the passage:</h4>
          <p class="passage-text">${question.content.passage}</p>
        </div>
        <div class="reading-question">
          <h4>${question.content.question}</h4>
          <div class="reading-options">
            ${options}
          </div>
        </div>
        <p class="natural-response-note">Choose the answer that makes the most sense to you.</p>
      </div>
    `;
  }

  /**
   * Setup event listeners for preliminary questions
   */
  setupQuestionEventListeners(question, questionNumber) {
    const nextBtn = document.getElementById('next-question-btn');

    // Next button click handler
    nextBtn.addEventListener('click', () => {
      this.handleQuestionResponse(question, questionNumber);
    });

    // Setup specific event listeners based on question type
    switch (question.type) {
      case 'grammar':
        this.setupGrammarListeners(question, questionNumber);
        break;
      case 'vocabulary':
        this.setupVocabularyListeners(question, questionNumber);
        break;
      case 'sentence':
        this.setupSentenceListeners(question, questionNumber);
        break;
      case 'drag-drop':
        this.setupDragDropListeners(question, questionNumber);
        break;
      case 'interactive-grammar':
        this.setupInteractiveGrammarListeners(question, questionNumber);
        break;
      case 'vocab-matching':
        this.setupVocabMatchingListeners(question, questionNumber);
        break;
      case 'reading-comprehension':
        this.setupReadingComprehensionListeners(question, questionNumber);
        break;
    }
  }



  /**
   * Setup grammar question listeners
   */
  setupGrammarListeners(question, questionNumber) {
    const textarea = document.getElementById(`grammar-response-${questionNumber}`);

    textarea.addEventListener('input', () => {
      const nextBtn = document.getElementById('next-question-btn');
      // Allow any response - no minimum length requirement
      nextBtn.disabled = false;
    });
  }

  /**
   * Setup vocabulary question listeners
   */
  setupVocabularyListeners(question, questionNumber) {
    const options = document.querySelectorAll(`input[name="vocab-${questionNumber}"]`);

    options.forEach(option => {
      option.addEventListener('change', () => {
        const nextBtn = document.getElementById('next-question-btn');
        nextBtn.disabled = false;

        // Visual feedback
        document.querySelectorAll('.vocab-option').forEach(opt => opt.classList.remove('selected'));
        option.closest('.vocab-option').classList.add('selected');
      });
    });
  }

  /**
   * Setup sentence construction listeners
   */
  setupSentenceListeners(question, questionNumber) {
    const textarea = document.getElementById(`sentence-response-${questionNumber}`);

    textarea.addEventListener('input', () => {
      const nextBtn = document.getElementById('next-question-btn');
      // Allow any response - no validation required
      nextBtn.disabled = false;
    });
  }
  /**
   * Setup drag-and-drop listeners
   */
  setupDragDropListeners(question, questionNumber) {
    const dropZone = document.getElementById(`drop-zone-${questionNumber}`);
    const wordBank = document.getElementById(`word-bank-${questionNumber}`);
    const dragWords = wordBank.querySelectorAll('.drag-word');
    let droppedWords = [];

    // Setup drag events for words
    dragWords.forEach(word => {
      word.addEventListener('dragstart', (e) => {
        e.dataTransfer.setData('text/plain', word.dataset.word);
        e.dataTransfer.setData('text/index', word.dataset.index);
      });
    });

    // Setup drop zone events
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropZone.classList.add('drag-over');
    });

    dropZone.addEventListener('dragleave', () => {
      dropZone.classList.remove('drag-over');
    });

    dropZone.addEventListener('drop', (e) => {
      e.preventDefault();
      dropZone.classList.remove('drag-over');

      const word = e.dataTransfer.getData('text/plain');
      const index = e.dataTransfer.getData('text/index');

      // Add word to drop zone
      droppedWords.push(word);
      this.updateDropZone(questionNumber, droppedWords);

      // Hide the dragged word from word bank
      const draggedWord = wordBank.querySelector(`[data-index="${index}"]`);
      if (draggedWord) {
        draggedWord.style.display = 'none';
      }

      // Enable next button (allow any arrangement)
      const nextBtn = document.getElementById('next-question-btn');
      nextBtn.disabled = false;
    });

    // Allow clicking to reset
    dropZone.addEventListener('click', () => {
      droppedWords = [];
      this.updateDropZone(questionNumber, droppedWords);
      // Show all words again
      dragWords.forEach(word => word.style.display = 'block');
      const nextBtn = document.getElementById('next-question-btn');
      nextBtn.disabled = true;
    });
  }

  /**
   * Update drop zone display
   */
  updateDropZone(questionNumber, words) {
    const dropZone = document.getElementById(`drop-zone-${questionNumber}`);
    if (words.length === 0) {
      dropZone.innerHTML = '<p class="drop-placeholder">Drag words here to build your sentence</p>';
    } else {
      dropZone.innerHTML = `<p class="constructed-sentence">${words.join(' ')}</p>`;
    }
  }

  /**
   * Setup interactive grammar listeners
   */
  setupInteractiveGrammarListeners(question, questionNumber) {
    const interactiveWords = document.querySelectorAll(`#sentence-interactive-${questionNumber} .interactive-word`);
    const correctionsDisplay = document.getElementById(`corrections-${questionNumber}`);
    let selectedWords = [];

    interactiveWords.forEach(word => {
      word.addEventListener('click', () => {
        const wordText = word.dataset.word;
        const position = parseInt(word.dataset.position);

        if (word.classList.contains('selected')) {
          // Deselect word
          word.classList.remove('selected');
          selectedWords = selectedWords.filter(w => w.position !== position);
        } else {
          // Select word
          word.classList.add('selected');
          selectedWords.push({word: wordText, position: position});
        }

        this.updateCorrectionsDisplay(questionNumber, selectedWords);

        // Enable next button (allow any selection)
        const nextBtn = document.getElementById('next-question-btn');
        nextBtn.disabled = false;
      });
    });
  }

  /**
   * Update corrections display
   */
  updateCorrectionsDisplay(questionNumber, selectedWords) {
    const correctionsDisplay = document.getElementById(`corrections-${questionNumber}`);
    if (selectedWords.length === 0) {
      correctionsDisplay.innerHTML = '';
    } else {
      const wordsText = selectedWords.map(w => w.word).join(', ');
      correctionsDisplay.innerHTML = `<p class="selected-corrections">Selected words: ${wordsText}</p>`;
    }
  }

  /**
   * Setup vocabulary matching listeners
   */
  setupVocabMatchingListeners(question, questionNumber) {
    const words = document.querySelectorAll('.match-word');
    const definitions = document.querySelectorAll('.match-definition');
    const matchesDisplay = document.getElementById(`matches-${questionNumber}`);
    let selectedWord = null;
    let matches = [];

    words.forEach(word => {
      word.addEventListener('click', () => {
        // Clear previous selections
        words.forEach(w => w.classList.remove('selected'));
        // Select this word
        word.classList.add('selected');
        selectedWord = word.dataset.word;
      });
    });

    definitions.forEach(definition => {
      definition.addEventListener('click', () => {
        if (selectedWord) {
          // Create match
          matches.push({word: selectedWord, definition: definition.dataset.definition});
          this.updateMatchesDisplay(questionNumber, matches);

          // Clear selections
          words.forEach(w => w.classList.remove('selected'));
          selectedWord = null;

          // Enable next button (allow any matches)
          const nextBtn = document.getElementById('next-question-btn');
          nextBtn.disabled = false;
        }
      });
    });
  }

  /**
   * Update matches display
   */
  updateMatchesDisplay(questionNumber, matches) {
    const matchesDisplay = document.getElementById(`matches-${questionNumber}`);
    if (matches.length === 0) {
      matchesDisplay.innerHTML = '';
    } else {
      const matchesHtml = matches.map(match =>
        `<div class="match-pair">${match.word} → ${match.definition}</div>`
      ).join('');
      matchesDisplay.innerHTML = `<div class="matches-list">${matchesHtml}</div>`;
    }
  }

  /**
   * Setup reading comprehension listeners
   */
  setupReadingComprehensionListeners(question, questionNumber) {
    const options = document.querySelectorAll(`input[name="reading-${questionNumber}"]`);

    options.forEach(option => {
      option.addEventListener('change', () => {
        // Enable next button when any option is selected
        const nextBtn = document.getElementById('next-question-btn');
        nextBtn.disabled = false;
      });
    });
  }





  /**
   * Check if sentence is complete
   */
  checkSentenceCompletion(questionNumber) {
    const sentenceBuilder = document.getElementById(`sentence-builder-${questionNumber}`);
    const words = sentenceBuilder.querySelectorAll('.sentence-word');
    const nextBtn = document.getElementById('next-question-btn');

    // Allow any response - no minimum word requirement
    nextBtn.disabled = false;
  }

  /**
   * Reset sentence builder
   */
  resetSentenceBuilder(questionNumber) {
    const sentenceBuilder = document.getElementById(`sentence-builder-${questionNumber}`);
    const wordBank = document.querySelector('.word-bank');

    // Clear sentence builder
    sentenceBuilder.innerHTML = '<div class="drop-zone">Drop words here to build your sentence</div>';

    // Show all word tiles
    const wordTiles = wordBank.querySelectorAll('.word-tile');
    wordTiles.forEach(tile => {
      tile.style.display = 'block';
    });

    // Disable next button
    const nextBtn = document.getElementById('next-question-btn');
    nextBtn.disabled = true;
  }

  /**
   * Handle question response and move to next question
   */
  handleQuestionResponse(question, questionNumber) {
    const responseEndTime = new Date();
    const timeSpent = this.questionStartTime ?
      Math.round((responseEndTime - this.questionStartTime) / 1000) : 0;

    const response = this.collectQuestionResponse(question, questionNumber);

    // Store basic response for AI analysis (backward compatibility)
    this.preliminaryResponses.push({
      question: question,
      response: response,
      questionNumber: questionNumber
    });

    // Store detailed response for transparency
    const detailedResponse = {
      questionNumber: questionNumber,
      questionType: question.type,
      questionText: question.question || `${question.type} question`,
      questionContent: question.content,
      correctAnswer: question.correctAnswer,
      studentResponse: this.extractStudentResponseText(response),
      timestamp: responseEndTime,
      timeSpent: timeSpent,
      questionStartTime: this.questionStartTime,
      responseEndTime: responseEndTime
    };

    this.detailedResponses.preliminaryQuestions.push(detailedResponse);

    console.log(`Question ${questionNumber} detailed response:`, detailedResponse);

    // Move to next question or proceed to essay
    this.currentQuestionIndex++;

    if (this.currentQuestionIndex < this.preliminaryQuestions.length) {
      // Show next question with smooth transition
      this.transitionToNextQuestion();
    } else {
      // All preliminary questions completed, proceed to essay
      this.proceedToEssay();
    }
  }

  /**
   * Extract readable student response text from response object
   */
  extractStudentResponseText(response) {
    if (!response) return '';

    switch (response.type) {
      case 'grammar':
        return response.correctedSentence || '';
      case 'vocabulary':
        return response.selectedAnswer || '';
      case 'sentence':
        return response.constructedSentence || '';
      case 'drag-drop':
        return response.constructedSentence || '';
      case 'interactive-grammar':
        return response.selectedWords.map(w => w.word).join(', ') || '';
      case 'vocab-matching':
        return response.studentMatchesString || '';
      case 'reading-comprehension':
        return response.selectedAnswer || '';
      default:
        return JSON.stringify(response);
    }
  }

  /**
   * Collect response based on question type
   */
  collectQuestionResponse(question, questionNumber) {
    switch (question.type) {
      case 'grammar':
        return this.collectGrammarResponse(question, questionNumber);
      case 'vocabulary':
        return this.collectVocabularyResponse(question, questionNumber);
      case 'sentence':
        return this.collectSentenceResponse(question, questionNumber);
      case 'drag-drop':
        return this.collectDragDropResponse(question, questionNumber);
      case 'interactive-grammar':
        return this.collectInteractiveGrammarResponse(question, questionNumber);
      case 'vocab-matching':
        return this.collectVocabMatchingResponse(question, questionNumber);
      case 'reading-comprehension':
        return this.collectReadingComprehensionResponse(question, questionNumber);
      default:
        return null;
    }
  }



  /**
   * Collect grammar question response
   */
  collectGrammarResponse(question, questionNumber) {
    const textarea = document.getElementById(`grammar-response-${questionNumber}`);
    return {
      type: 'grammar',
      originalSentence: question.content,
      correctedSentence: textarea.value.trim(),
      expectedAnswer: question.correctAnswer
    };
  }

  /**
   * Collect vocabulary question response
   */
  collectVocabularyResponse(question, questionNumber) {
    const selectedOption = document.querySelector(`input[name="vocab-${questionNumber}"]:checked`);
    return {
      type: 'vocabulary',
      question: question.content,
      selectedAnswer: selectedOption ? selectedOption.value : null,
      correctAnswer: question.correctAnswer,
      options: question.options
    };
  }

  /**
   * Collect sentence construction response
   */
  collectSentenceResponse(question, questionNumber) {
    const textarea = document.getElementById(`sentence-response-${questionNumber}`);
    const constructedSentence = textarea.value.trim();

    return {
      type: 'sentence',
      originalWords: question.content,
      constructedSentence: constructedSentence,
      correctAnswer: question.correctAnswer
    };
  }

  /**
   * Collect drag-and-drop response
   */
  collectDragDropResponse(question, questionNumber) {
    const dropZone = document.getElementById(`drop-zone-${questionNumber}`);
    const constructedSentence = dropZone.querySelector('.constructed-sentence');

    return {
      type: 'drag-drop',
      originalWords: question.content,
      shuffledWords: question.shuffledWords,
      constructedSentence: constructedSentence ? constructedSentence.textContent : '',
      correctAnswer: question.correctAnswer
    };
  }

  /**
   * Collect interactive grammar response
   */
  collectInteractiveGrammarResponse(question, questionNumber) {
    const selectedWords = document.querySelectorAll(`#sentence-interactive-${questionNumber} .interactive-word.selected`);
    const selectedWordsData = Array.from(selectedWords).map(word => ({
      word: word.dataset.word,
      position: parseInt(word.dataset.position)
    }));

    return {
      type: 'interactive-grammar',
      originalSentence: question.content,
      selectedWords: selectedWordsData,
      correctAnswer: question.correctAnswer,
      expectedMistakes: question.mistakes
    };
  }

  /**
   * Collect vocabulary matching response
   */
  collectVocabMatchingResponse(question, questionNumber) {
    const matchesDisplay = document.getElementById(`matches-${questionNumber}`);
    const matchPairs = matchesDisplay.querySelectorAll('.match-pair');
    const matches = {};

    matchPairs.forEach(pair => {
      const text = pair.textContent;
      const [word, definition] = text.split(' → ');
      if (word && definition) {
        matches[word.trim()] = definition.trim();
      }
    });

    // Convert matches to string format for consistency
    const matchesString = Object.entries(matches)
      .map(([word, def]) => `${word}→${def}`)
      .join('; ');

    return {
      type: 'vocab-matching',
      words: question.content.words,
      definitions: question.content.definitions,
      studentMatches: matches,
      studentMatchesString: matchesString,
      correctAnswer: question.correctAnswer
    };
  }

  /**
   * Collect reading comprehension response
   */
  collectReadingComprehensionResponse(question, questionNumber) {
    const selectedOption = document.querySelector(`input[name="reading-${questionNumber}"]:checked`);

    return {
      type: 'reading-comprehension',
      passage: question.content.passage,
      question: question.content.question,
      options: question.content.options,
      selectedAnswer: selectedOption ? selectedOption.value : null,
      correctAnswer: question.correctAnswer
    };
  }

  /**
   * Smooth transition to next question
   */
  transitionToNextQuestion() {
    const container = document.getElementById('preliminary-questions-container');

    // Fade out current question
    container.style.opacity = '0';
    container.style.transform = 'translateY(-10px)';

    setTimeout(() => {
      this.renderCurrentQuestion();

      // Fade in new question
      container.style.opacity = '1';
      container.style.transform = 'translateY(0)';
    }, 300);
  }

  /**
   * Proceed to essay writing task
   */
  proceedToEssay() {
    console.log('All preliminary questions completed. Proceeding to essay...');
    console.log('Preliminary responses:', this.preliminaryResponses);

    this.currentStage = 'essay';
    this.essayStartTime = new Date(); // Start timing essay phase

    // Hide preliminary questions container with smooth transition
    const prelimContainer = document.getElementById('preliminary-questions-container');
    if (prelimContainer) {
      prelimContainer.style.opacity = '0';
      prelimContainer.style.transform = 'translateY(-20px)';

      setTimeout(() => {
        prelimContainer.style.display = 'none';
        this.showEssayContainer();
      }, 300);
    } else {
      this.showEssayContainer();
    }
  }

  /**
   * Show essay container with smooth transition
   */
  showEssayContainer() {
    const essayContainer = document.querySelector('.english-question-container');
    if (essayContainer) {
      // Update header text to reflect completion of preliminary questions
      const headerText = document.querySelector('.english-header p');
      if (headerText) {
        headerText.textContent = 'Great! Now complete the writing task below. Continue to write as much as possible to help us assess your English level.';
      }

      // Show with smooth transition
      essayContainer.style.opacity = '0';
      essayContainer.style.transform = 'translateY(20px)';
      essayContainer.style.display = 'block';

      setTimeout(() => {
        essayContainer.style.opacity = '1';
        essayContainer.style.transform = 'translateY(0)';
      }, 100);
    }

    // Setup essay event listeners
    this.setupEssayEventListeners();
    this.updateCharacterCount();
  }

  /**
   * Setup event listeners for essay stage
   */
  setupEssayEventListeners() {
    const textarea = document.getElementById('english-response');
    const submitBtn = document.getElementById('submit-english-assessment');

    // Character count and submit button state
    textarea.addEventListener('input', () => {
      this.updateCharacterCount();
      this.updateSubmitButtonState();
    });

    // Submit assessment
    submitBtn.addEventListener('click', () => {
      this.submitAssessment();
    });
  }

  /**
   * Setup event listeners for the assessment
   */
  setupEventListeners() {
    const textarea = document.getElementById('english-response');
    const submitBtn = document.getElementById('submit-english-assessment');
    const returnBtn = document.getElementById('return-to-portal');

    // Character count and submit button state
    textarea.addEventListener('input', () => {
      this.updateCharacterCount();
      this.updateSubmitButtonState();
    });

    // Submit assessment
    submitBtn.addEventListener('click', () => {
      this.submitAssessment();
    });

    // Return to portal button
    if (returnBtn) {
      returnBtn.addEventListener('click', () => {
        // Set intentional navigation flag to prevent beforeunload warnings
        if (typeof window.intentionalNavigation !== 'undefined') {
          window.intentionalNavigation = true;
        }
        window.location.href = 'https://barefootelearning.etraininglibrary.com/';
      });
    }

    // Note: Removed beforeunload warning for students as their journey ends with English assessment
    // Students should be able to navigate freely after completing their assessment
  }

  /**
   * Start the countdown timer
   */
  startTimer() {
    this.updateTimerDisplay();

    this.timerInterval = setInterval(() => {
      this.timeRemaining--;
      this.updateTimerDisplay();

      if (this.timeRemaining <= 0) {
        this.timeUp();
      }
    }, 1000);
  }

  /**
   * Update the timer display
   */
  updateTimerDisplay() {
    const timerElement = document.getElementById('english-timer');
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;

    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    timerElement.textContent = timeString;

    // Add warning classes based on time remaining
    timerElement.classList.remove('warning', 'critical');

    if (this.timeRemaining <= 300) { // 5 minutes
      timerElement.classList.add('critical');
    } else if (this.timeRemaining <= 600) { // 10 minutes
      timerElement.classList.add('warning');
    }
  }

  /**
   * Handle time up scenario
   */
  timeUp() {
    clearInterval(this.timerInterval);

    // Capture essay data for timeout submission
    const textarea = document.getElementById('english-response');
    const response = textarea ? textarea.value.trim() : '';
    const submissionTime = new Date();
    this.captureEssayResponseData(response, submissionTime, 'timeout');

    // Auto-submit regardless of response length when time is up
    alert('Time is up! Your response will be submitted as is.');
    this.submitAssessment();
  }

  /**
   * Capture detailed essay response data
   */
  captureEssayResponseData(response, submissionTime, submissionType) {
    const essayTimeSpent = this.essayStartTime ?
      Math.round((submissionTime - this.essayStartTime) / 1000) : 0;

    // Get essay prompt from the page
    const promptElement = document.querySelector('.english-question h3');
    const prompt = promptElement ? promptElement.textContent : 'English writing assessment';

    this.detailedResponses.essayResponse = {
      prompt: prompt,
      response: response,
      wordCount: response.split(/\s+/).filter(word => word.length > 0).length,
      characterCount: response.length,
      timestamp: submissionTime,
      timeSpent: essayTimeSpent,
      submissionType: submissionType, // 'manual' or 'timeout'
      essayStartTime: this.essayStartTime,
      submissionTime: submissionTime
    };

    // Finalize assessment metadata
    this.detailedResponses.assessmentMetadata.endTime = submissionTime;
    this.detailedResponses.assessmentMetadata.totalDuration = this.assessmentStartTime ?
      Math.round((submissionTime - this.assessmentStartTime) / 1000) : 0;

    console.log('Essay response data captured:', this.detailedResponses.essayResponse);
  }

  /**
   * Update character count display
   */
  updateCharacterCount() {
    const textarea = document.getElementById('english-response');
    const charCountElement = document.getElementById('char-count');
    const currentLength = textarea.value.length;

    charCountElement.textContent = currentLength;

    // Change color based on length
    if (currentLength >= 4500) {
      charCountElement.style.color = '#dc2626'; // Red
    } else if (currentLength >= 4000) {
      charCountElement.style.color = '#ea580c'; // Orange
    } else {
      charCountElement.style.color = '#64748b'; // Default gray
    }
  }

  /**
   * Update submit button state - allow any response length
   */
  updateSubmitButtonState() {
    const textarea = document.getElementById('english-response');
    const submitBtn = document.getElementById('submit-english-assessment');

    // Always enable submit button - allow any response length including empty
    submitBtn.disabled = false;
  }

  /**
   * Submit the English assessment
   */
  async submitAssessment() {
    if (this.isSubmitted) return;

    const textarea = document.getElementById('english-response');
    const response = textarea.value.trim();
    const submissionTime = new Date();

    // Capture detailed essay response data
    this.captureEssayResponseData(response, submissionTime, 'manual');

    // Allow submission of any response length - short responses indicate lower proficiency

    this.isSubmitted = true;
    clearInterval(this.timerInterval);

    // Show enhanced loading state with professional animation
    const submitBtn = document.getElementById('submit-english-assessment');
    const analysisStartTime = Date.now();
    this.showAnalysisLoadingState(submitBtn);

    try {
      // Get user information
      const email = document.getElementById('email').value.trim();
      const userType = document.querySelector('input[name="user-type"]:checked')?.value;
      const studentLevel = document.getElementById('student-level').value;

      // Send to AI for analysis with preliminary responses
      const analysisResult = await this.analyzeEnglishProficiency(response, email, studentLevel, this.preliminaryResponses);

      // Store results in database with detailed response data
      await this.storeEnglishAssessmentResults(email, response, analysisResult, this.detailedResponses);

      // Ensure minimum analysis time for better UX (shows thoroughness)
      const analysisDuration = Date.now() - analysisStartTime;
      const minAnalysisTime = 3000; // 3 seconds minimum for AI analysis perception
      const remainingTime = Math.max(0, minAnalysisTime - analysisDuration);

      setTimeout(() => {
        // Show completion results for all students regardless of score
        // All students receive their English assessment report and journey ends here
        this.showCompletionResults(analysisResult);
      }, remainingTime);

    } catch (error) {
      console.error('Error submitting English assessment:', error);
      alert('An error occurred while processing your assessment. Please try again.');

      // Reset button state
      this.resetSubmitButton(submitBtn);
      this.isSubmitted = false;
    }
  }

  /**
   * Show enhanced loading state during analysis
   */
  showAnalysisLoadingState(submitBtn) {
    const loadingStates = [
      '<span class="btn-text">Analyzing your response...</span>',
      '<span class="btn-text">Evaluating grammar...</span>',
      '<span class="btn-text">Assessing vocabulary...</span>',
      '<span class="btn-text">Reviewing structure...</span>',
      '<span class="btn-text">Generating feedback...</span>'
    ];

    let currentIndex = 0;
    submitBtn.innerHTML = loadingStates[currentIndex];
    submitBtn.disabled = true;

    // Add loading animation class
    submitBtn.classList.add('loading-animation');

    // Cycle through loading messages
    const loadingInterval = setInterval(() => {
      currentIndex = (currentIndex + 1) % loadingStates.length;
      submitBtn.innerHTML = loadingStates[currentIndex];
    }, 1500);

    // Store interval for cleanup
    submitBtn.dataset.loadingInterval = loadingInterval;
  }

  /**
   * Reset submit button to original state
   */
  resetSubmitButton(submitBtn) {
    if (submitBtn.dataset.loadingInterval) {
      clearInterval(Number(submitBtn.dataset.loadingInterval));
      delete submitBtn.dataset.loadingInterval;
    }

    submitBtn.classList.remove('loading-animation');
    submitBtn.innerHTML = '<span class="btn-text">Submit Assessment</span><span class="btn-icon">→</span>';
    submitBtn.disabled = false;
  }

  /**
   * Send response to AI for analysis
   */
  async analyzeEnglishProficiency(response, email, studentLevel, preliminaryResponses = []) {
    const analysisResponse = await fetch('/api/analyze-english-proficiency', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        response: response,
        email: email,
        studentLevel: studentLevel,
        timeSpent: this.timeLimit - this.timeRemaining,
        preliminaryResponses: preliminaryResponses
      }),
    });

    if (!analysisResponse.ok) {
      throw new Error(`Analysis failed: ${analysisResponse.statusText}`);
    }

    return await analysisResponse.json();
  }

  /**
   * Store English assessment results in database
   */
  async storeEnglishAssessmentResults(email, response, analysisResult, detailedResponses = null) {
    const userType = document.querySelector('input[name="user-type"]:checked')?.value;
    const studentLevel = document.getElementById('student-level').value;

    // Check if Firebase and global variables are available
    if (typeof firebase === 'undefined') {
      throw new Error('Firebase not initialized');
    }

    if (typeof db === 'undefined') {
      throw new Error('Firestore database not initialized');
    }

    if (typeof userCompany === 'undefined' || !userCompany) {
      throw new Error('User company not set');
    }

    // Prepare update data
    const updateData = {
      englishProficiencyScore: analysisResult.score,
      englishProficiencyLevel: analysisResult.level,
      englishAssessmentCompleted: true,
      englishResponse: response,
      englishAssessmentTimestamp: firebase.firestore.FieldValue.serverTimestamp(),
      timeSpentOnEnglish: this.timeLimit - this.timeRemaining,
      // Store detailed feedback
      englishFeedback: analysisResult.feedback || {
        grammar: 'Assessment completed',
        vocabulary: 'Vocabulary evaluated',
        coherence: 'Structure assessed',
        overall: 'Overall proficiency evaluated'
      },
      englishStrengths: analysisResult.strengths || ['Completed the assessment'],
      englishImprovements: analysisResult.improvements || ['Continue practicing English'],
      // Store course recommendations based on English proficiency level
      courseRecommendations: analysisResult.courseRecommendations || {
        eligible: [],
        description: 'Course recommendations will be provided by your instructor',
        nextSteps: 'Please contact your instructor for guidance'
      },
      updatedAt: firebase.firestore.FieldValue.serverTimestamp()
    };

    // Add comprehensive response data for transparency
    if (detailedResponses) {
      updateData.englishAssessmentResponses = {
        preliminaryQuestions: detailedResponses.preliminaryQuestions || [],
        essayResponse: detailedResponses.essayResponse || null,
        assessmentMetadata: detailedResponses.assessmentMetadata || {}
      };
    }

    // Update user document with English assessment data including detailed responses
    const companyRef = db.collection('companies').doc(userCompany);
    const userRef = companyRef.collection('users').doc(email);

    await userRef.update(updateData);

    console.log('English assessment results stored with detailed responses:', {
      score: analysisResult.score,
      level: analysisResult.level,
      email: email,
      feedbackStored: !!analysisResult.feedback,
      detailedResponsesStored: !!detailedResponses,
      preliminaryQuestionsCount: detailedResponses?.preliminaryQuestions?.length || 0,
      essayResponseStored: !!detailedResponses?.essayResponse
    });
  }



  /**
   * Show completion results for all students - journey ends with English assessment report
   */
  showCompletionResults(analysisResult) {
    console.log('Student completed English assessment:', analysisResult);

    // Set intentional navigation flag - students can now navigate freely
    if (typeof window.intentionalNavigation !== 'undefined') {
      window.intentionalNavigation = true;
    }

    // Clean up any loading intervals
    const submitBtn = document.getElementById('submit-english-assessment');
    if (submitBtn && submitBtn.dataset.loadingInterval) {
      clearInterval(Number(submitBtn.dataset.loadingInterval));
      delete submitBtn.dataset.loadingInterval;
    }

    // Use smooth transition to hide assessment and show results
    this.transitionToResults(analysisResult);
  }

  /**
   * Smooth transition from assessment to results
   */
  transitionToResults(analysisResult) {
    const assessmentContainer = document.getElementById('english-assessment-container');
    const completionContainer = document.getElementById('english-completion-container');

    if (!assessmentContainer || !completionContainer) {
      console.error('Required containers not found for transition');
      return;
    }

    // Add transition styles
    assessmentContainer.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

    // Fade out assessment container
    assessmentContainer.style.opacity = '0';
    assessmentContainer.style.transform = 'translateY(-20px)';

    setTimeout(() => {
      // Hide assessment container
      assessmentContainer.style.display = 'none';

      // Populate results container
      this.populateResultsContainer(analysisResult);

      // Prepare completion container for smooth entrance
      completionContainer.style.opacity = '0';
      completionContainer.style.transform = 'translateY(20px)';
      completionContainer.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      completionContainer.style.display = 'flex';

      // Trigger smooth fade-in for results
      setTimeout(() => {
        completionContainer.style.opacity = '1';
        completionContainer.style.transform = 'translateY(0)';
      }, 50);
    }, 500);
  }

  /**
   * Generate course recommendations based on English proficiency level
   */
  generateCourseRecommendations(analysisResult) {
    const score = analysisResult.score;
    const level = analysisResult.level;

    let recommendations = {
      eligible: [],
      description: '',
      nextSteps: ''
    };

    if (score >= 16) {
      // Level 2 English Achievement (L2/GCSE)
      recommendations.eligible = [
        'Level 3 Digital Skills Course',
        'Level 3 Health and Social Care Course',
        'Level 2 Digital Skills Course (and below)',
        'Level 2 Health and Social Care Course'
      ];
      recommendations.description = 'Congratulations! You have achieved Level 2 English proficiency. You are eligible for our most advanced courses.';
      recommendations.nextSteps = 'You can proceed directly to Level 3 courses or choose from any lower level courses based on your interests and career goals.';
    } else if (score >= 10) {
      // Level 1 English Assessment
      recommendations.eligible = [
        'Level 2 Health and Social Care Course',
        'Level 2 Digital Skills Course (and below)',
        'Level 2 English Course',
        'Level 1 Digital Skills Courses'
      ];
      recommendations.description = 'You have achieved Level 1 English proficiency. You have access to a good range of Level 2 courses.';
      recommendations.nextSteps = 'Consider taking Level 2 courses to advance your skills, or take a Level 2 English course to work toward Level 2 English proficiency.';
    } else {
      // Entry Level Assessment (9 points or below)
      recommendations.eligible = [
        'Beginners Digital Skills Course',
        'Beginners Plus Digital Skills Course',
        'Level 1 English Course',
        'Entry Level Health & Social Care Courses',
        'Basic English Support Courses'
      ];
      recommendations.description = 'You are at Entry Level English proficiency. We recommend starting with our foundational courses.';
      recommendations.nextSteps = 'Begin with beginner courses and English support to build your foundation, then progress to Level 1 English to unlock more advanced opportunities.';
    }

    return recommendations;
  }

  /**
   * Populate results container with detailed analysis for all students
   */
  populateResultsContainer(analysisResult) {
    const container = document.getElementById('english-completion-container');

    if (!container) {
      console.error('English completion container not found');
      return;
    }

    // Use course recommendations from server analysis, or generate as fallback
    const courseRecommendations = analysisResult.courseRecommendations || this.generateCourseRecommendations(analysisResult);

    // Create detailed results HTML for all students
    const resultsHTML = `
      <div class="results-wrapper">
        <div class="results-header">
          <h2>Assessment Complete</h2>
          <div class="score-display">
            <span class="score-value">${analysisResult.score}/21</span>
            <span class="score-level">${analysisResult.level} Level</span>
          </div>
        </div>
        <div class="results-content">
          <div class="feedback-section">
            <h3>Your Performance Analysis</h3>
            <div class="feedback-grid">
              <div class="feedback-item">
                <h4>Grammar</h4>
                <p>${analysisResult.feedback?.grammar || 'Assessment completed'}</p>
              </div>
              <div class="feedback-item">
                <h4>Vocabulary</h4>
                <p>${analysisResult.feedback?.vocabulary || 'Vocabulary evaluated'}</p>
              </div>
              <div class="feedback-item">
                <h4>Organization</h4>
                <p>${analysisResult.feedback?.coherence || 'Structure assessed'}</p>
              </div>
            </div>
            <div class="overall-feedback">
              <h4>Overall Assessment</h4>
              <p>${analysisResult.feedback?.overall || 'Assessment completed successfully'}</p>
            </div>
          </div>

          <div class="strengths-improvements">
            <div class="strengths-section">
              <h4>Your Strengths</h4>
              <ul>
                ${(analysisResult.strengths || ['Completed the assessment']).map(strength => `<li>${strength}</li>`).join('')}
              </ul>
            </div>
            <div class="improvements-section">
              <h4>Areas for Improvement</h4>
              <ul>
                ${(analysisResult.improvements || ['Continue practicing English']).map(improvement => `<li>${improvement}</li>`).join('')}
              </ul>
            </div>
          </div>

          <div class="course-recommendations">
            <h4>Recommended Courses</h4>
            <div class="recommendation-description">
              <p>${courseRecommendations.description}</p>
            </div>
            <div class="eligible-courses">
              <h5>You are eligible for:</h5>
              <ul class="course-list">
                ${courseRecommendations.eligible.map(course => `<li class="course-item">${course}</li>`).join('')}
              </ul>
            </div>
            <div class="next-steps-guidance">
              <h5>Next Steps</h5>
              <p>${courseRecommendations.nextSteps}</p>
            </div>
          </div>

          <div class="contact-info">
            <h4>Get Started</h4>
            <p>Contact your instructor or advisor to discuss these course options and begin your learning journey. Your assessment results will help them guide you to the most suitable courses for your current level.</p>
          </div>

          <div class="results-actions">
            <button id="return-to-portal" class="results-btn primary">Return to Portal</button>
          </div>
        </div>
      </div>
    `;

    container.innerHTML = resultsHTML;

    // Add event listener for return to portal button
    const returnBtn = document.getElementById('return-to-portal');
    if (returnBtn) {
      returnBtn.addEventListener('click', () => {
        // Set intentional navigation flag to prevent beforeunload warnings
        if (typeof window.intentionalNavigation !== 'undefined') {
          window.intentionalNavigation = true;
        }
        window.location.href = 'https://barefootelearning.etraininglibrary.com/';
      });
    }
  }

  /**
   * Clean up timer and event listeners
   */
  destroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }
}

// Global instance
window.englishAssessment = null;

/**
 * Initialize English assessment for student users
 */
async function initializeEnglishAssessment() {
  if (window.englishAssessment) {
    window.englishAssessment.destroy();
  }

  window.englishAssessment = new EnglishAssessment();
  await window.englishAssessment.init();
}

/**
 * Check if user should take English assessment
 */
function shouldTakeEnglishAssessment() {
  const userType = document.querySelector('input[name="user-type"]:checked')?.value;
  return userType === 'student';
}
